/* Modern Material Design 3 Theme */
@import '@angular/material/prebuilt-themes/azure-blue.css';

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Global Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* App Loading Screen */
.app-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #fef7ff 0%, #f3edf7 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-container {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.loading-logo {
  animation: logoFloat 3s ease-in-out infinite;
}

.loading-logo svg {
  filter: drop-shadow(0 4px 8px rgba(0, 100, 149, 0.2));
}

.loading-text {
  font-family: var(--font-family-sans);
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #006495, #64597c);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.02em;
}

.loading-spinner {
  position: relative;
}

.spinner-ring {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 100, 149, 0.1);
  border-top: 3px solid #006495;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Material Design 3 Design Tokens */
:root {
  /* Typography */
  --font-family-sans: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'SF Mono', Consolas, monospace;

  /* Primary Palette */
  --md-sys-color-primary: #006495;
  --md-sys-color-on-primary: #ffffff;
  --md-sys-color-primary-container: #c7e7ff;
  --md-sys-color-on-primary-container: #001e30;
  --md-sys-color-primary-rgb: 0, 100, 149;
  
  /* Secondary Palette */
  --md-sys-color-secondary: #50606e;
  --md-sys-color-on-secondary: #ffffff;
  --md-sys-color-secondary-container: #d3e5f5;
  --md-sys-color-on-secondary-container: #0c1929;
  
  /* Tertiary Palette */
  --md-sys-color-tertiary: #64597c;
  --md-sys-color-on-tertiary: #ffffff;
  --md-sys-color-tertiary-container: #eaddff;
  --md-sys-color-on-tertiary-container: #201935;
  
  /* Surface Palette */
  --md-sys-color-surface: #fef7ff;
  --md-sys-color-on-surface: #1c1b1f;
  --md-sys-color-surface-variant: #e7e0ec;
  --md-sys-color-on-surface-variant: #49454f;
  --md-sys-color-surface-container-lowest: #ffffff;
  --md-sys-color-surface-container-low: #f7f2fa;
  --md-sys-color-surface-container: #f3edf7;
  --md-sys-color-surface-container-high: #ede6f0;
  --md-sys-color-surface-container-highest: #e8e0e5;
  
  /* Background */
  --md-sys-color-background: #fef7ff;
  --md-sys-color-on-background: #1c1b1f;
  
  /* Error */
  --md-sys-color-error: #ba1a1a;
  --md-sys-color-on-error: #ffffff;
  --md-sys-color-error-container: #ffdad6;
  --md-sys-color-on-error-container: #410002;
  
  /* Warning */
  --md-sys-color-warning: #8c5000;
  --md-sys-color-on-warning: #ffffff;
  --md-sys-color-warning-container: #ffdbbb;
  --md-sys-color-on-warning-container: #2d1600;
  
  /* Success */
  --md-sys-color-success: #006e26;
  --md-sys-color-on-success: #ffffff;
  --md-sys-color-success-container: #78ff89;
  --md-sys-color-on-success-container: #002106;
  
  /* Shape */
  --md-sys-shape-corner-none: 0px;
  --md-sys-shape-corner-extra-small: 4px;
  --md-sys-shape-corner-small: 8px;
  --md-sys-shape-corner-medium: 12px;
  --md-sys-shape-corner-large: 16px;
  --md-sys-shape-corner-extra-large: 28px;
  --md-sys-shape-corner-full: 50%;
  
  /* Elevation Shadows */
  --md-sys-elevation-level0: none;
  --md-sys-elevation-level1: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level2: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level3: 0px 4px 8px 3px rgba(0, 0, 0, 0.15), 0px 1px 3px 0px rgba(0, 0, 0, 0.3);
  --md-sys-elevation-level4: 0px 6px 10px 4px rgba(0, 0, 0, 0.15), 0px 2px 3px 0px rgba(0, 0, 0, 0.3);
  --md-sys-elevation-level5: 0px 8px 12px 6px rgba(0, 0, 0, 0.15), 0px 4px 4px 0px rgba(0, 0, 0, 0.3);
  
  /* Motion */
  --md-sys-motion-duration-short1: 50ms;
  --md-sys-motion-duration-short2: 100ms;
  --md-sys-motion-duration-short3: 150ms;
  --md-sys-motion-duration-short4: 200ms;
  --md-sys-motion-duration-medium1: 250ms;
  --md-sys-motion-duration-medium2: 300ms;
  --md-sys-motion-duration-medium3: 350ms;
  --md-sys-motion-duration-medium4: 400ms;
  
  --md-sys-motion-easing-standard: cubic-bezier(0.2, 0, 0, 1);
  --md-sys-motion-easing-emphasized: cubic-bezier(0.2, 0, 0, 1);
  --md-sys-motion-easing-emphasized-decelerate: cubic-bezier(0.05, 0.7, 0.1, 1);
  
  /* Legacy Support */
  --primary-color: var(--md-sys-color-primary);
  --primary-color-light: var(--md-sys-color-primary-container);
  --surface-color: var(--md-sys-color-surface-container-low);
  --background-color: var(--md-sys-color-background);
  --primary-text: var(--md-sys-color-on-surface);
  --secondary-text: var(--md-sys-color-on-surface-variant);
  --error-color: var(--md-sys-color-error);
  --success-color: var(--md-sys-color-success);
  --warning-color: var(--md-sys-color-warning);
  --shadow-elevation-1: var(--md-sys-elevation-level1);
  --shadow-elevation-2: var(--md-sys-elevation-level2);
  --shadow-elevation-3: var(--md-sys-elevation-level3);
}

/* Dark Theme */
[data-theme="dark"] {
  --md-sys-color-primary: #8fceff;
  --md-sys-color-on-primary: #003548;
  --md-sys-color-primary-container: #004c69;
  --md-sys-color-on-primary-container: #c7e7ff;
  
  --md-sys-color-secondary: #b7c9d9;
  --md-sys-color-on-secondary: #21323f;
  --md-sys-color-secondary-container: #384956;
  --md-sys-color-on-secondary-container: #d3e5f5;
  
  --md-sys-color-surface: #10141b;
  --md-sys-color-on-surface: #e3e2e6;
  --md-sys-color-surface-variant: #42474e;
  --md-sys-color-on-surface-variant: #c2c7cf;
  --md-sys-color-surface-container-lowest: #0b0f16;
  --md-sys-color-surface-container-low: #1d1b20;
  --md-sys-color-surface-container: #191c23;
  --md-sys-color-surface-container-high: #232025;
  --md-sys-color-surface-container-highest: #2e2a2f;
  
  --md-sys-color-background: #10141b;
  --md-sys-color-on-background: #e3e2e6;

  /* Standardized Component Tokens */
  --md-comp-button-height-small: 32px;
  --md-comp-button-height-medium: 40px;
  --md-comp-button-height-large: 48px;
  --md-comp-button-height-extra-large: 56px;

  --md-comp-input-height-small: 32px;
  --md-comp-input-height-medium: 40px;
  --md-comp-input-height-large: 48px;
  --md-comp-input-height-extra-large: 56px;

  /* Standardized Animation Tokens */
  --md-sys-motion-duration-short1: 50ms;
  --md-sys-motion-duration-short2: 100ms;
  --md-sys-motion-duration-short3: 150ms;
  --md-sys-motion-duration-short4: 200ms;
  --md-sys-motion-duration-medium1: 250ms;
  --md-sys-motion-duration-medium2: 300ms;
  --md-sys-motion-duration-medium3: 350ms;
  --md-sys-motion-duration-medium4: 400ms;
  --md-sys-motion-duration-long1: 450ms;
  --md-sys-motion-duration-long2: 500ms;
  --md-sys-motion-duration-long3: 550ms;
  --md-sys-motion-duration-long4: 600ms;

  --md-sys-motion-easing-linear: cubic-bezier(0, 0, 1, 1);
  --md-sys-motion-easing-standard: cubic-bezier(0.2, 0, 0, 1);
  --md-sys-motion-easing-standard-accelerate: cubic-bezier(0.3, 0, 1, 1);
  --md-sys-motion-easing-standard-decelerate: cubic-bezier(0, 0, 0, 1);
  --md-sys-motion-easing-emphasized: cubic-bezier(0.2, 0, 0, 1);
  --md-sys-motion-easing-emphasized-accelerate: cubic-bezier(0.3, 0, 0.8, 0.15);
  --md-sys-motion-easing-emphasized-decelerate: cubic-bezier(0.05, 0.7, 0.1, 1);

  /* Legacy Color Compatibility (to be phased out) */
  --primary-color: var(--md-sys-color-primary);
  --primary-color-light: var(--md-sys-color-primary-container);
  --secondary-text: var(--md-sys-color-on-surface-variant);
  --primary-text: var(--md-sys-color-on-surface);
  --surface-color: var(--md-sys-color-surface-container-low);
  --background-color: var(--md-sys-color-surface-container-lowest);
  --error-color: var(--md-sys-color-error);
}

/* Base Styles */
html, body {
  height: 100%;
  font-family: var(--font-family-sans);
  background-color: var(--md-sys-color-background);
  color: var(--md-sys-color-on-background);
  line-height: 1.5;
  font-size: 14px;
  letter-spacing: 0.25px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

/* Typography Classes */
.headline-large {
  font-size: 32px;
  font-weight: 400;
  line-height: 40px;
  letter-spacing: 0px;
}

.headline-medium {
  font-size: 28px;
  font-weight: 400;
  line-height: 36px;
  letter-spacing: 0px;
}

.headline-small {
  font-size: 24px;
  font-weight: 400;
  line-height: 32px;
  letter-spacing: 0px;
}

.title-large {
  font-size: 22px;
  font-weight: 400;
  line-height: 28px;
  letter-spacing: 0px;
}

.title-medium {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0.15px;
}

.title-small {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.1px;
}

.body-large {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0.5px;
}

.body-medium {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.25px;
}

.body-small {
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: 0.4px;
}

/* Standardized Component Classes */

/* Button Standardization */
.md-button-base {
  border-radius: var(--md-sys-shape-corner-full) !important;
  font-family: var(--font-family-sans) !important;
  font-weight: 500 !important;
  letter-spacing: 0.1px !important;
  text-transform: none !important;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard) !important;
  border: none !important;
  cursor: pointer !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
  position: relative !important;
  overflow: hidden !important;
}

.md-button-small {
  height: var(--md-comp-button-height-small) !important;
  padding: 0 1rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

.md-button-medium {
  height: var(--md-comp-button-height-medium) !important;
  padding: 0 1.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

.md-button-large {
  height: var(--md-comp-button-height-large) !important;
  padding: 0 2rem !important;
  font-size: 1rem !important;
  line-height: 1.5rem !important;
}

.md-button-extra-large {
  height: var(--md-comp-button-height-extra-large) !important;
  padding: 0 2.5rem !important;
  font-size: 1rem !important;
  line-height: 1.5rem !important;
}

/* Button Variants */
.md-button-filled {
  background: var(--md-sys-color-primary) !important;
  color: var(--md-sys-color-on-primary) !important;
  box-shadow: var(--md-sys-elevation-level1) !important;
}

.md-button-filled:hover:not(:disabled) {
  background: color-mix(in srgb, var(--md-sys-color-primary) 92%, var(--md-sys-color-on-primary)) !important;
  box-shadow: var(--md-sys-elevation-level2) !important;
  transform: translateY(-1px) !important;
}

.md-button-filled:active:not(:disabled) {
  background: color-mix(in srgb, var(--md-sys-color-primary) 88%, var(--md-sys-color-on-primary)) !important;
  box-shadow: var(--md-sys-elevation-level1) !important;
  transform: translateY(0) !important;
}

.md-button-outlined {
  background: transparent !important;
  color: var(--md-sys-color-primary) !important;
  border: 1px solid var(--md-sys-color-outline) !important;
}

.md-button-outlined:hover:not(:disabled) {
  background: var(--md-sys-color-primary-container) !important;
  border-color: var(--md-sys-color-primary) !important;
  transform: translateY(-1px) !important;
}

.md-button-text {
  background: transparent !important;
  color: var(--md-sys-color-primary) !important;
  box-shadow: none !important;
}

.md-button-text:hover:not(:disabled) {
  background: var(--md-sys-color-primary-container) !important;
}

/* Icon Button Standardization */
.md-icon-button {
  width: 40px !important;
  height: 40px !important;
  border-radius: var(--md-sys-shape-corner-full) !important;
  background: transparent !important;
  border: none !important;
  cursor: pointer !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard) !important;
  color: var(--md-sys-color-on-surface-variant) !important;
}

.md-icon-button:hover:not(:disabled) {
  background: var(--md-sys-color-surface-container-highest) !important;
  color: var(--md-sys-color-on-surface) !important;
  transform: scale(1.05) !important;
}

.md-icon-button:active:not(:disabled) {
  transform: scale(0.95) !important;
}

.md-icon-button-small {
  width: 32px !important;
  height: 32px !important;
}

.md-icon-button-large {
  width: 48px !important;
  height: 48px !important;
}

/* Input Field Standardization */
.md-input-base {
  font-family: var(--font-family-sans) !important;
  font-size: 1rem !important;
  line-height: 1.5rem !important;
  letter-spacing: 0.009375em !important;
  border-radius: var(--md-sys-shape-corner-extra-small) !important;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard) !important;
  border: 1px solid var(--md-sys-color-outline) !important;
  background: var(--md-sys-color-surface-container-highest) !important;
  color: var(--md-sys-color-on-surface) !important;
  padding: 0 1rem !important;
}

.md-input-base:focus {
  outline: none !important;
  border-color: var(--md-sys-color-primary) !important;
  box-shadow: 0 0 0 1px var(--md-sys-color-primary) !important;
}

.md-input-base:hover:not(:focus):not(:disabled) {
  border-color: var(--md-sys-color-on-surface) !important;
}

.md-input-base::placeholder {
  color: var(--md-sys-color-on-surface-variant) !important;
  opacity: 0.7 !important;
}

.md-input-base:disabled {
  background: var(--md-sys-color-surface-variant) !important;
  color: var(--md-sys-color-on-surface-variant) !important;
  border-color: var(--md-sys-color-outline-variant) !important;
  cursor: not-allowed !important;
}

.md-input-small {
  height: var(--md-comp-input-height-small) !important;
  font-size: 0.875rem !important;
  padding: 0 0.75rem !important;
}

.md-input-medium {
  height: var(--md-comp-input-height-medium) !important;
}

.md-input-large {
  height: var(--md-comp-input-height-large) !important;
  font-size: 1.125rem !important;
  padding: 0 1.25rem !important;
}

.md-input-extra-large {
  height: var(--md-comp-input-height-extra-large) !important;
  font-size: 1.125rem !important;
  padding: 0 1.5rem !important;
}

/* Search Input Variant */
.md-search-input {
  border-radius: var(--md-sys-shape-corner-full) !important;
  background: var(--md-sys-color-surface-variant) !important;
  border: 1px solid var(--md-sys-color-outline-variant) !important;
  padding-left: 2.5rem !important;
}

.md-search-input:focus {
  background: var(--md-sys-color-surface-container-high) !important;
  border-color: var(--md-sys-color-primary) !important;
}

/* Card Standardization */
.md-card-base {
  background: var(--md-sys-color-surface-container-low) !important;
  border: 1px solid var(--md-sys-color-outline-variant) !important;
  border-radius: var(--md-sys-shape-corner-large) !important;
  box-shadow: var(--md-sys-elevation-level1) !important;
  transition: all var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard) !important;
  overflow: hidden !important;
}

.md-card-base:hover {
  box-shadow: var(--md-sys-elevation-level2) !important;
  transform: translateY(-1px) !important;
}

.md-card-elevated {
  box-shadow: var(--md-sys-elevation-level2) !important;
}

.md-card-elevated:hover {
  box-shadow: var(--md-sys-elevation-level3) !important;
  transform: translateY(-2px) !important;
}

/* Chip Standardization */
.md-chip-base {
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  padding: 0.375rem 0.75rem !important;
  border-radius: var(--md-sys-shape-corner-small) !important;
  font-family: var(--font-family-sans) !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  line-height: 1.25rem !important;
  letter-spacing: 0.1px !important;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard) !important;
  cursor: pointer !important;
  border: 1px solid transparent !important;
}

.md-chip-outlined {
  background: transparent !important;
  color: var(--md-sys-color-on-surface) !important;
  border-color: var(--md-sys-color-outline) !important;
}

.md-chip-outlined:hover {
  background: var(--md-sys-color-surface-container-highest) !important;
}

.md-chip-filled {
  background: var(--md-sys-color-surface-container-high) !important;
  color: var(--md-sys-color-on-surface) !important;
}

.md-chip-filled:hover {
  background: var(--md-sys-color-surface-container-highest) !important;
}

.md-chip-selected {
  background: var(--md-sys-color-secondary-container) !important;
  color: var(--md-sys-color-on-secondary-container) !important;
  border-color: var(--md-sys-color-secondary) !important;
}

.label-large {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.1px;
}

.label-medium {
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  letter-spacing: 0.5px;
}

.label-small {
  font-size: 11px;
  font-weight: 500;
  line-height: 16px;
  letter-spacing: 0.5px;
}

/* App Container */
.app-container {
  display: flex;
  height: 100vh;
  background: var(--md-sys-color-background);
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--md-sys-color-surface-container-lowest);
  border-radius: var(--md-sys-shape-corner-large) 0 0 0;
  overflow: hidden;
  margin-left: 4px;
}

/* Enhanced Material Components */
.mat-mdc-card {
  border-radius: var(--md-sys-shape-corner-medium) !important;
  box-shadow: var(--md-sys-elevation-level1) !important;
  background: var(--md-sys-color-surface-container-low) !important;
  color: var(--md-sys-color-on-surface) !important;
  border: none !important;
  transition: all var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard) !important;
}

.mat-mdc-card:hover {
  box-shadow: var(--md-sys-elevation-level2) !important;
  transform: translateY(-2px);
}

.mat-mdc-card.elevated {
  box-shadow: var(--md-sys-elevation-level3) !important;
}

.mat-mdc-button, .mat-mdc-unelevated-button, .mat-mdc-raised-button {
  border-radius: var(--md-sys-shape-corner-full) !important;
  font-family: var(--font-family-sans) !important;
  font-weight: 500 !important;
  letter-spacing: 0.1px !important;
  text-transform: none !important;
  transition: all var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard) !important;
}

.mat-mdc-fab {
  border-radius: var(--md-sys-shape-corner-large) !important;
  box-shadow: var(--md-sys-elevation-level3) !important;
}

.mat-mdc-chip {
  border-radius: var(--md-sys-shape-corner-small) !important;
  font-family: var(--font-family-sans) !important;
}

/* Scrollbars */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-extra-small);
}

::-webkit-scrollbar-thumb {
  background: var(--md-sys-color-primary);
  border-radius: var(--md-sys-shape-corner-extra-small);
  transition: background var(--md-sys-motion-duration-short2);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--md-sys-color-on-primary-container);
}

/* State Layers */
.state-layer {
  position: relative;
  overflow: hidden;
}

.state-layer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: currentColor;
  opacity: 0;
  transition: opacity var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
  pointer-events: none;
}

.state-layer:hover::before {
  opacity: 0.08;
}

.state-layer:focus-visible::before {
  opacity: 0.12;
}

.state-layer:active::before {
  opacity: 0.12;
}

/* Motion */
.motion-fade-in {
  animation: motionFadeIn var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
}

.motion-slide-up {
  animation: motionSlideUp var(--md-sys-motion-duration-medium3) var(--md-sys-motion-easing-emphasized-decelerate);
}

.motion-scale-in {
  animation: motionScaleIn var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-emphasized-decelerate);
}

@keyframes motionFadeIn {
  from { 
    opacity: 0; 
    transform: translateY(8px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes motionSlideUp {
  from { 
    opacity: 0; 
    transform: translateY(40px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes motionScaleIn {
  from { 
    opacity: 0; 
    transform: scale(0.8); 
  }
  to { 
    opacity: 1; 
    transform: scale(1); 
  }
}

/* Container Classes */
.primary-container {
  background: var(--md-sys-color-primary-container) !important;
  color: var(--md-sys-color-on-primary-container) !important;
}

.secondary-container {
  background: var(--md-sys-color-secondary-container) !important;
  color: var(--md-sys-color-on-secondary-container) !important;
}

.tertiary-container {
  background: var(--md-sys-color-tertiary-container) !important;
  color: var(--md-sys-color-on-tertiary-container) !important;
}

.error-container {
  background: var(--md-sys-color-error-container) !important;
  color: var(--md-sys-color-on-error-container) !important;
}

.success-container {
  background: var(--md-sys-color-success-container) !important;
  color: var(--md-sys-color-on-success-container) !important;
}

.warning-container {
  background: var(--md-sys-color-warning-container) !important;
  color: var(--md-sys-color-on-warning-container) !important;
}

/* Responsive Design */
@media (max-width: 840px) {
  .main-content {
    margin-left: 0;
    border-radius: 0;
  }
}