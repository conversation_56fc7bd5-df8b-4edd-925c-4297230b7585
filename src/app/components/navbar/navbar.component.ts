import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatBadgeModule } from '@angular/material/badge';
import { MatRippleModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { ThemeService } from '../../services/theme.service';
import { LanguageService, Language } from '../../services/language.service';
import { MockDataService } from '../../services/mock-data.service';
import { SystemHealth } from '../../models';

@Component({
  selector: 'app-navbar',
  standalone: true,
  imports: [
    CommonModule, 
    MatButtonModule, 
    MatIconModule, 
    MatMenuModule,
    MatTooltipModule,
    MatBadgeModule,
    MatRippleModule,
    MatDividerModule
  ],
  templateUrl: './navbar.component.html',
  styleUrl: './navbar.component.css'
})
export class NavbarComponent implements OnInit {
  title = 'Assistant';
  isDarkTheme = false;
  systemHealth: SystemHealth | null = null;
  currentTime = new Date();
  notifications = 3;
  isOnline = true;

  quickActions = [
    {
      icon: 'search',
      label: 'Search',
      tooltip: 'Global search (⌘K)'
    },
    {
      icon: 'tune',
      label: 'Filters',
      tooltip: 'Quick filters'
    },
    {
      icon: 'bookmark',
      label: 'Bookmarks',
      tooltip: 'View bookmarks'
    }
  ];

  constructor(
    private themeService: ThemeService,
    private languageService: LanguageService,
    private mockDataService: MockDataService
  ) {
    themeService.isDarkTheme$.subscribe(
      isDark => this.isDarkTheme = isDark
    );

    setInterval(() => {
      this.currentTime = new Date();
    }, 1000);
  }

  ngOnInit() {
    this.mockDataService.getSystemHealth().subscribe(health => {
      this.systemHealth = health;
    });
  }

  toggleTheme() {
    this.themeService.toggleTheme();
  }

  setLanguage(lang: Language) {
    this.languageService.setLanguage(lang);
  }

  getSystemStatus(): string {
    if (!this.systemHealth) return 'Loading...';
    return this.systemHealth.overall.status === 'healthy' ? 'All Systems Operational' : 'System Issues Detected';
  }

  getSystemStatusClass(): string {
    if (!this.systemHealth) return 'loading';
    return this.systemHealth.overall.status === 'healthy' ? 'healthy' : 'warning';
  }

  onQuickAction(action: { icon: string; label: string; tooltip: string }) {
    console.log('Quick action:', action.label);
  }

  openNotifications() {
    console.log('Opening notifications');
  }

  openUserMenu() {
    console.log('Opening user menu');
  }

  openGlobalSearch() {
    console.log('Opening global search');
    // TODO: Implement global search modal
  }
}