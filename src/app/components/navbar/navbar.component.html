<div class="modern-navbar">
  <!-- Left Section -->
  <div class="navbar-left">
    <!-- Brand Logo -->
    <div class="brand-section">
      <div class="brand-logo">
        <mat-icon class="logo-icon">psychology</mat-icon>
      </div>
      <h1 class="headline-small brand-title">{{title}}</h1>
    </div>

    <!-- System Status -->
    <div class="system-status-section">
      <div class="status-indicator" [class]="getSystemStatusClass()">
        <div class="status-dot"></div>
        <span class="label-medium status-text">{{getSystemStatus()}}</span>
      </div>
      <div class="system-metrics" *ngIf="systemHealth">
        <div class="metric">
          <span class="title-medium metric-value">{{systemHealth.uptime}}h</span>
          <span class="label-small metric-label">Uptime</span>
        </div>
        <div class="metric">
          <span class="title-medium metric-value">{{systemHealth.api.responseTime}}ms</span>
          <span class="label-small metric-label">Response</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Center Section -->
  <div class="navbar-center">
    <!-- System Status Section -->
    <div class="system-status-section">
      <div class="status-indicator" [class]="getSystemStatusClass()">
        <div class="status-dot"></div>
        <span class="body-medium status-text">{{getSystemStatus()}}</span>
      </div>
    </div>

    <!-- Search Bar -->
    <div class="search-section">
      <div class="search-container">
        <mat-icon class="search-icon">search</mat-icon>
        <input
          type="text"
          class="search-input md-search-input md-input-base md-input-medium body-medium"
          placeholder="Search anything... ⌘K"
          (click)="openGlobalSearch()"
          readonly>
        <div class="search-shortcut">
          <kbd class="label-small">⌘</kbd>
          <kbd class="label-small">K</kbd>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <button
        *ngFor="let action of quickActions"
        mat-icon-button
        class="quick-action-btn md-icon-button"
        [matTooltip]="action.tooltip"
        matTooltipPosition="below"
        (click)="onQuickAction(action)"
        matRipple>
        <mat-icon>{{action.icon}}</mat-icon>
      </button>
    </div>
  </div>

  <!-- Right Section -->
  <div class="navbar-right">
    <!-- Time Display -->
    <div class="time-display">
      <div class="title-medium current-time">
        {{currentTime | date:'HH:mm:ss'}}
      </div>
      <div class="label-small current-date">
        {{currentTime | date:'MMM dd, y'}}
      </div>
    </div>

    <!-- Notifications -->
    <button
      mat-icon-button
      class="nav-action-btn md-icon-button"
      [matBadge]="notifications"
      matBadgeColor="warn"
      matBadgeSize="small"
      [matBadgeHidden]="notifications === 0"
      matTooltip="Notifications"
      matTooltipPosition="below"
      (click)="openNotifications()"
      matRipple>
      <mat-icon>notifications</mat-icon>
    </button>

    <!-- Language Selector -->
    <button
      mat-icon-button
      class="nav-action-btn md-icon-button"
      [matMenuTriggerFor]="languageMenu"
      matTooltip="Language"
      matTooltipPosition="below"
      matRipple>
      <mat-icon>language</mat-icon>
    </button>
    <mat-menu #languageMenu="matMenu" class="modern-menu">
      <button mat-menu-item (click)="setLanguage('en')" class="body-medium">
        <mat-icon>translate</mat-icon>
        <span>English</span>
      </button>
      <button mat-menu-item (click)="setLanguage('tc')" class="body-medium">
        <mat-icon>translate</mat-icon>
        <span>繁體中文</span>
      </button>
    </mat-menu>

    <!-- Theme Toggle -->
    <button
      mat-icon-button
      class="nav-action-btn md-icon-button theme-toggle"
      (click)="toggleTheme()"
      [matTooltip]="isDarkTheme ? 'Switch to light mode' : 'Switch to dark mode'"
      matTooltipPosition="below"
      matRipple>
      <mat-icon>{{ isDarkTheme ? 'light_mode' : 'dark_mode' }}</mat-icon>
    </button>

    <!-- User Menu -->
    <button
      mat-icon-button
      class="nav-action-btn md-icon-button user-avatar"
      [matMenuTriggerFor]="userMenu"
      matTooltip="Account"
      matTooltipPosition="below"
      matRipple>
      <div class="avatar-container">
        <div class="avatar">
          <mat-icon>person</mat-icon>
        </div>
        <div class="online-indicator" [class.online]="isOnline"></div>
      </div>
    </button>
    <mat-menu #userMenu="matMenu" class="modern-menu">
      <div class="user-info">
        <div class="user-avatar">
          <mat-icon>person</mat-icon>
        </div>
        <div class="user-details">
          <div class="title-medium user-name">Assistant User</div>
          <div class="label-small user-role">Developer</div>
        </div>
      </div>
      <mat-divider></mat-divider>
      <button mat-menu-item class="body-medium">
        <mat-icon>account_circle</mat-icon>
        <span>Profile</span>
      </button>
      <button mat-menu-item class="body-medium">
        <mat-icon>settings</mat-icon>
        <span>Settings</span>
      </button>
      <button mat-menu-item class="body-medium">
        <mat-icon>help</mat-icon>
        <span>Help & Support</span>
      </button>
      <mat-divider></mat-divider>
      <button mat-menu-item class="body-medium logout-item">
        <mat-icon>logout</mat-icon>
        <span>Sign Out</span>
      </button>
    </mat-menu>
  </div>
</div>