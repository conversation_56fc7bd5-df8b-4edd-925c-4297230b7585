.modern-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72px;
  padding: 0 2rem;
  background: linear-gradient(90deg, 
    var(--md-sys-color-surface-container) 0%,
    var(--md-sys-color-surface-container-low) 50%,
    var(--md-sys-color-surface-container) 100%);
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
  position: sticky;
  top: 0;
  z-index: 50;
  backdrop-filter: blur(20px);
  transition: all var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
}

.modern-navbar::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--md-sys-color-primary) 50%, 
    transparent 100%);
  opacity: 0.3;
}

/* Left Section */
.navbar-left {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
  min-width: 0;
}

.brand-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-logo {
  width: 32px;
  height: 32px;
  border-radius: var(--md-sys-shape-corner-medium);
  background: linear-gradient(135deg, 
    var(--md-sys-color-primary-container),
    var(--md-sys-color-tertiary-container));
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--md-sys-elevation-level1);
  position: relative;
  overflow: hidden;
}

.brand-logo::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
  100% { transform: translateX(100%); }
}

.logo-icon {
  color: var(--md-sys-color-primary);
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.brand-title {
  margin: 0;
  background: linear-gradient(135deg, 
    var(--md-sys-color-primary), 
    var(--md-sys-color-tertiary));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.02em;
}

.system-status-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-full);
  border: 1px solid var(--md-sys-color-outline-variant);
  transition: all var(--md-sys-motion-duration-short2);
}

.status-indicator.healthy {
  background: var(--md-sys-color-success-container);
  border-color: var(--md-sys-color-success);
}

.status-indicator.warning {
  background: var(--md-sys-color-warning-container);
  border-color: var(--md-sys-color-warning);
}

.status-indicator.loading {
  background: var(--md-sys-color-primary-container);
  border-color: var(--md-sys-color-primary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--md-sys-color-outline);
  animation: pulse 2s infinite;
}

.status-indicator.healthy .status-dot {
  background: var(--md-sys-color-success);
}

.status-indicator.warning .status-dot {
  background: var(--md-sys-color-warning);
}

.status-indicator.loading .status-dot {
  background: var(--md-sys-color-primary);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  color: var(--md-sys-color-on-surface);
  white-space: nowrap;
}

.system-metrics {
  display: flex;
  gap: 1rem;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.125rem;
}

.metric-value {
  color: var(--md-sys-color-primary);
  line-height: 1;
}

.metric-label {
  color: var(--md-sys-color-on-surface-variant);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  line-height: 1;
}

/* Center Section */
.navbar-center {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 2;
  justify-content: center;
  min-width: 0;
}

.quick-actions {
  display: flex;
  gap: 0.25rem;
}

/* Quick Action Buttons - Override to use MD3 standards */
.quick-action-btn.md-icon-button {
  /* Remove conflicting styles and let MD3 classes take precedence */
  color: var(--md-sys-color-on-surface-variant) !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: var(--md-sys-shape-corner-full) !important;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard) !important;
  background: transparent !important;
  border: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.quick-action-btn.md-icon-button:hover:not(:disabled) {
  background: var(--md-sys-color-surface-container-highest) !important;
  color: var(--md-sys-color-on-surface) !important;
  transform: scale(1.05) !important;
}

/* System Status Section */
.system-status-section {
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  background: var(--md-sys-color-surface-container);
  border: 1px solid var(--md-sys-color-outline-variant);
  border-radius: var(--md-sys-shape-corner-full);
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
}

.status-indicator.online {
  background: var(--md-sys-color-primary-container);
  border-color: var(--md-sys-color-primary);
}

.status-indicator.offline {
  background: var(--md-sys-color-error-container);
  border-color: var(--md-sys-color-error);
}

.status-indicator.maintenance {
  background: var(--md-sys-color-tertiary-container);
  border-color: var(--md-sys-color-tertiary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--md-sys-color-primary);
  animation: statusPulse 2s infinite;
}

.status-indicator.online .status-dot {
  background: var(--md-sys-color-primary);
}

.status-indicator.offline .status-dot {
  background: var(--md-sys-color-error);
}

.status-indicator.maintenance .status-dot {
  background: var(--md-sys-color-tertiary);
}

.status-text {
  color: var(--md-sys-color-on-surface);
  font-weight: 500;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.search-section {
  max-width: 400px;
  width: 100%;
}

/* Search Container - Enhanced MD3 styling */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--md-sys-color-surface-variant);
  border: 1px solid var(--md-sys-color-outline-variant);
  border-radius: var(--md-sys-shape-corner-full);
  padding: 0.5rem 1rem;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
  cursor: pointer;
  height: var(--md-comp-input-height-medium);
  min-height: var(--md-comp-input-height-medium);
}

.search-container:hover {
  background: var(--md-sys-color-surface-container-high);
  border-color: var(--md-sys-color-primary);
  box-shadow: var(--md-sys-elevation-level1);
  transform: translateY(-1px);
}

.search-container:focus-within {
  background: var(--md-sys-color-surface-container-high);
  border-color: var(--md-sys-color-primary);
  box-shadow: 0 0 0 1px var(--md-sys-color-primary);
}

.search-icon {
  color: var(--md-sys-color-on-surface-variant);
  font-size: 1.2rem !important;
  width: 1.2rem !important;
  height: 1.2rem !important;
  margin-right: 0.75rem;
}

/* Search Input - Enhanced MD3 styling */
.search-input.md-search-input.md-input-base {
  flex: 1;
  border: none !important;
  background: transparent !important;
  color: var(--md-sys-color-on-surface) !important;
  outline: none !important;
  min-width: 0;
  height: auto !important;
  padding: 0 !important;
  font-family: var(--font-family-sans) !important;
  font-size: 1rem !important;
  line-height: 1.5rem !important;
  letter-spacing: 0.009375em !important;
}

.search-input.md-search-input.md-input-base::placeholder {
  color: var(--md-sys-color-on-surface-variant) !important;
  opacity: 0.7 !important;
}

.search-input.md-search-input.md-input-base:focus {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

.search-shortcut {
  display: flex;
  gap: 0.125rem;
  margin-left: 0.5rem;
}

.search-shortcut kbd {
  background: var(--md-sys-color-surface-container);
  border: 1px solid var(--md-sys-color-outline-variant);
  border-radius: var(--md-sys-shape-corner-extra-small);
  padding: 0.125rem 0.375rem;
  line-height: 1;
}

/* Right Section */
.navbar-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  justify-content: flex-end;
}

.time-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.125rem;
  padding: 0.5rem 0.75rem;
  background: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-medium);
  border: 1px solid var(--md-sys-color-outline-variant);
}

.current-time {
  color: var(--md-sys-color-primary);
  line-height: 1;
  font-variant-numeric: tabular-nums;
}

.current-date {
  color: var(--md-sys-color-on-surface-variant);
  line-height: 1;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Navigation Action Buttons - Override to use MD3 standards */
.nav-action-btn.md-icon-button {
  color: var(--md-sys-color-on-surface-variant) !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: var(--md-sys-shape-corner-full) !important;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard) !important;
  background: transparent !important;
  border: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative;
}

.nav-action-btn.md-icon-button:hover:not(:disabled) {
  background: var(--md-sys-color-surface-container-highest) !important;
  color: var(--md-sys-color-on-surface) !important;
  transform: scale(1.05) !important;
}

.nav-action-btn.md-icon-button:active:not(:disabled) {
  transform: scale(0.95) !important;
}

/* Theme Toggle Button - Special styling */
.theme-toggle.md-icon-button:hover:not(:disabled) {
  background: var(--md-sys-color-tertiary-container) !important;
  color: var(--md-sys-color-tertiary) !important;
  transform: scale(1.05) !important;
}

/* User Avatar Button - Enhanced MD3 styling */
.user-avatar.md-icon-button {
  padding: 0 !important;
  width: 48px !important;
  height: 48px !important;
  border-radius: var(--md-sys-shape-corner-full) !important;
}

.avatar-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Avatar Styling - Enhanced MD3 */
.avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--md-sys-shape-corner-full);
  background: linear-gradient(135deg,
    var(--md-sys-color-primary-container),
    var(--md-sys-color-secondary-container));
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--md-sys-color-outline-variant);
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
}

.user-avatar.md-icon-button:hover .avatar {
  border-color: var(--md-sys-color-primary);
  transform: scale(1.05);
  box-shadow: var(--md-sys-elevation-level1);
}

.avatar mat-icon {
  color: var(--md-sys-color-primary);
  font-size: 1.5rem !important;
  width: 1.5rem !important;
  height: 1.5rem !important;
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--md-sys-color-outline);
  border: 2px solid var(--md-sys-color-surface);
}

.online-indicator.online {
  background: var(--md-sys-color-success);
  animation: onlinePulse 2s infinite;
}

@keyframes onlinePulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Menu Styles */
.modern-menu {
  margin-top: 0.5rem;
  border-radius: var(--md-sys-shape-corner-large) !important;
  background: var(--md-sys-color-surface-container) !important;
  border: 1px solid var(--md-sys-color-outline-variant) !important;
  box-shadow: var(--md-sys-elevation-level3) !important;
  backdrop-filter: blur(20px);
}

.modern-menu .mat-mdc-menu-item {
  border-radius: var(--md-sys-shape-corner-medium) !important;
  margin: 0.25rem !important;
  transition: all var(--md-sys-motion-duration-short2) !important;
}

.modern-menu .mat-mdc-menu-item:hover {
  background: var(--md-sys-color-primary-container) !important;
  color: var(--md-sys-color-primary) !important;
}

.modern-menu .mat-mdc-menu-item mat-icon {
  margin-right: 0.75rem !important;
  color: inherit !important;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--md-sys-color-surface-variant);
  margin: 0.5rem;
  border-radius: var(--md-sys-shape-corner-medium);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--md-sys-shape-corner-full);
  background: linear-gradient(135deg, 
    var(--md-sys-color-primary-container),
    var(--md-sys-color-secondary-container));
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--md-sys-color-outline-variant);
}

.user-avatar mat-icon {
  color: var(--md-sys-color-primary);
  font-size: 1.5rem !important;
  width: 1.5rem !important;
  height: 1.5rem !important;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.user-name {
  color: var(--md-sys-color-on-surface);
}

.user-role {
  color: var(--md-sys-color-on-surface-variant);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.logout-item {
  color: var(--md-sys-color-error) !important;
}

.logout-item:hover {
  background: var(--md-sys-color-error-container) !important;
  color: var(--md-sys-color-on-error-container) !important;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .search-section {
    max-width: 300px;
  }
  
  .system-metrics {
    display: none;
  }
}

@media (max-width: 1024px) {
  .modern-navbar {
    padding: 0 1rem;
  }

  .navbar-center {
    flex: 1;
    gap: 1rem;
  }

  .quick-actions {
    display: none;
  }

  .time-display {
    display: none;
  }

  .system-status-section {
    display: none;
  }
}

@media (max-width: 768px) {
  .modern-navbar {
    height: 56px;
    padding: 0 0.75rem;
  }
  
  .navbar-left {
    gap: 1rem;
  }
  
  .brand-title {
    display: none;
  }
  
  .status-text {
    display: none;
  }
  
  .search-section {
    max-width: 200px;
  }
  
  .search-shortcut {
    display: none;
  }
  
  .navbar-right {
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .search-section {
    display: none;
  }
  
  .system-status-section {
    display: none;
  }
  
  .navbar-left,
  .navbar-right {
    flex: unset;
  }
  
  .navbar-center {
    display: none;
  }
}

/* Dark Theme Adjustments */
[data-theme="dark"] .modern-navbar {
  background: linear-gradient(90deg, 
    var(--md-sys-color-surface-container) 0%,
    var(--md-sys-color-surface-container-low) 50%,
    var(--md-sys-color-surface-container) 100%);
  border-bottom-color: var(--md-sys-color-outline);
}

[data-theme="dark"] .search-container {
  background: var(--md-sys-color-surface-container-low);
}

[data-theme="dark"] .user-info {
  background: var(--md-sys-color-surface-container-low);
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .modern-navbar {
    border-bottom-width: 2px;
  }
  
  .search-container,
  .status-indicator,
  .time-display {
    border-width: 2px;
  }
  
  .avatar {
    border-width: 3px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .modern-navbar,
  .nav-action-btn,
  .quick-action-btn,
  .search-container,
  .avatar,
  .brand-logo::before {
    transition: none;
    animation: none;
  }
  
  .status-dot,
  .online-indicator {
    animation: none;
  }
}