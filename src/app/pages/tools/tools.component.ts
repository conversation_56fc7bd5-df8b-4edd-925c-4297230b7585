import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatRippleModule } from '@angular/material/core';
import { MockDataService } from '../../services/mock-data.service';
import { Tool, ToolCategory } from '../../models';

@Component({
  selector: 'app-tools',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatTabsModule,
    MatProgressBarModule,
    MatBadgeModule,
    MatTooltipModule,
    MatRippleModule
  ],
  templateUrl: './tools.component.html',
  styleUrl: './tools.component.css'
})
export class ToolsComponent implements OnInit {
  tools: Tool[] = [];
  filteredTools: Tool[] = [];
  selectedCategory: ToolCategory | 'all' = 'all';
  searchForm: FormGroup;

  categories: { value: ToolCategory | 'all'; label: string; icon: string }[] = [
    { value: 'all', label: 'All Tools', icon: 'apps' },
    { value: 'code-analysis', label: 'Code Analysis', icon: 'code' },
    { value: 'database', label: 'Database', icon: 'storage' },
    { value: 'infrastructure', label: 'Infrastructure', icon: 'cloud' },
    { value: 'ai-operations', label: 'AI Operations', icon: 'psychology' },
    { value: 'file-management', label: 'File Management', icon: 'folder' },
    { value: 'testing', label: 'Testing', icon: 'bug_report' },
    { value: 'deployment', label: 'Deployment', icon: 'rocket_launch' }
  ];

  constructor(
    private fb: FormBuilder,
    private mockDataService: MockDataService
  ) {
    this.searchForm = this.fb.group({
      searchTerm: [''],
      category: ['all']
    });
  }

  ngOnInit() {
    this.loadTools();
    this.setupSearch();
  }

  loadTools() {
    this.mockDataService.getTools().subscribe(tools => {
      this.tools = tools;
      this.filterTools();
    });
  }

  setupSearch() {
    this.searchForm.valueChanges.subscribe(() => {
      this.filterTools();
    });
  }

  filterTools() {
    const { searchTerm, category } = this.searchForm.value;

    this.filteredTools = this.tools.filter(tool => {
      const matchesSearch = !searchTerm ||
        tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tool.description.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCategory = category === 'all' || tool.category === category;

      return matchesSearch && matchesCategory;
    });
  }

  selectCategory(category: ToolCategory | 'all') {
    this.selectedCategory = category;
    this.searchForm.patchValue({ category });
  }

  getToolIcon(tool: Tool): string {
    return tool.icon || this.getCategoryIcon(tool.category);
  }

  getCategoryIcon(category: ToolCategory): string {
    const categoryMap = {
      'code-analysis': 'code',
      'database': 'storage',
      'infrastructure': 'cloud',
      'ai-operations': 'psychology',
      'file-management': 'folder',
      'testing': 'bug_report',
      'deployment': 'rocket_launch'
    };
    return categoryMap[category] || 'build';
  }

  getPopularityColor(popularity: number): string {
    if (popularity >= 80) return 'primary';
    if (popularity >= 60) return 'accent';
    return 'warn';
  }

  executeTool(tool: Tool) {
    console.log('Executing tool:', tool.name);
    // TODO: Implement tool execution
  }

  viewToolDetails(tool: Tool) {
    console.log('Viewing tool details:', tool.name);
    // TODO: Implement tool details modal
  }

  trackByToolId(index: number, tool: Tool): string {
    return tool.id;
  }
}
