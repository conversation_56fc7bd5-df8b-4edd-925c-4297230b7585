.tools-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--md-sys-color-background);
  min-height: 100vh;
}

/* Header Section */
.tools-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  flex-wrap: wrap;
}

.title-section {
  flex: 1;
  min-width: 300px;
}

.title-section h1 {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 0 0 0.5rem 0;
  color: var(--md-sys-color-on-background);
}

.header-icon {
  font-size: 2.5rem !important;
  width: 2.5rem !important;
  height: 2.5rem !important;
  color: var(--md-sys-color-primary);
}

.header-subtitle {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
  max-width: 600px;
}

/* Search and Filters */
.search-filters {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
  flex-wrap: wrap;
}

.search-field {
  min-width: 300px;
  flex: 1;
}

.category-field {
  min-width: 200px;
}

/* Category Chips */
.category-chips {
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--md-sys-color-surface-container-low);
  border-radius: var(--md-sys-shape-corner-large);
  border: 1px solid var(--md-sys-color-outline-variant);
}

.category-chip {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  transition: all var(--md-sys-motion-duration-short2);
}

.category-chip:hover {
  transform: translateY(-2px);
  box-shadow: var(--md-sys-elevation-level2);
}

.category-chip.selected {
  background: var(--md-sys-color-primary-container) !important;
  color: var(--md-sys-color-on-primary-container) !important;
  border: 1px solid var(--md-sys-color-primary) !important;
}

/* Tools Grid */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.tool-card {
  background: var(--md-sys-color-surface-container-low);
  border: 1px solid var(--md-sys-color-outline-variant);
  border-radius: var(--md-sys-shape-corner-large);
  transition: all var(--md-sys-motion-duration-medium2);
  position: relative;
  overflow: hidden;
}

.tool-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--md-sys-elevation-level3);
  border-color: var(--md-sys-color-primary);
}

.tool-card.popular {
  border-color: var(--md-sys-color-primary);
  background: linear-gradient(135deg,
    var(--md-sys-color-surface-container-low) 0%,
    var(--md-sys-color-primary-container) 100%);
}

.tool-card.popular::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-top: 20px solid var(--md-sys-color-primary);
}

/* Tool Header */
.tool-card .mat-mdc-card-header {
  padding-bottom: 1rem;
}

.tool-icon {
  width: 48px !important;
  height: 48px !important;
  border-radius: var(--md-sys-shape-corner-medium);
  background: linear-gradient(135deg,
    var(--md-sys-color-primary-container),
    var(--md-sys-color-tertiary-container));
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--md-sys-color-outline-variant);
}

.tool-icon mat-icon {
  color: var(--md-sys-color-primary);
  font-size: 1.5rem !important;
  width: 1.5rem !important;
  height: 1.5rem !important;
}

.tool-badges {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
}

.popularity-badge {
  font-size: 1.2rem !important;
  width: 1.2rem !important;
  height: 1.2rem !important;
}

.usage-badge {
  color: var(--md-sys-color-on-surface-variant);
}

/* Tool Content */
.tool-description {
  color: var(--md-sys-color-on-surface);
  margin-bottom: 1rem;
  line-height: 1.5;
}

.popularity-section {
  margin-bottom: 1rem;
}

.popularity-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.popularity-value {
  color: var(--md-sys-color-primary);
  font-weight: 500;
}

/* Parameters Preview */
.parameters-preview {
  margin-top: 1rem;
}

.parameters-label {
  color: var(--md-sys-color-on-surface-variant);
  display: block;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.parameters-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.parameter-chip {
  background: var(--md-sys-color-surface-variant) !important;
  color: var(--md-sys-color-on-surface-variant) !important;
  font-size: 0.75rem !important;
  height: 24px !important;
  border-radius: var(--md-sys-shape-corner-small) !important;
}

.required-indicator {
  color: var(--md-sys-color-error);
  font-weight: bold;
  margin-left: 2px;
}

.more-params {
  color: var(--md-sys-color-on-surface-variant);
  font-style: italic;
}

/* Tool Actions */
.tool-card .mat-mdc-card-actions {
  padding-top: 1rem;
  border-top: 1px solid var(--md-sys-color-outline-variant);
}

.action-button {
  margin-right: 0.5rem;
}

.action-button mat-icon {
  margin-right: 0.5rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--md-sys-color-on-surface-variant);
}

.empty-icon {
  font-size: 4rem !important;
  width: 4rem !important;
  height: 4rem !important;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 1rem 0 0.5rem 0;
  color: var(--md-sys-color-on-surface);
}

.empty-state p {
  margin: 0;
  max-width: 400px;
  margin: 0 auto;
}