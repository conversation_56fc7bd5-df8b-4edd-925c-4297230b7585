<div class="tools-container">
  <!-- Header Section -->
  <div class="tools-header">
    <div class="header-content">
      <div class="title-section">
        <h1 class="display-small">
          <mat-icon class="header-icon">construction</mat-icon>
          AI Tools Explorer
        </h1>
        <p class="body-large header-subtitle">
          Discover and utilize powerful AI tools to enhance your development workflow
        </p>
      </div>

      <!-- Search and Filters -->
      <div class="search-filters" [formGroup]="searchForm">
        <mat-form-field appearance="outline" class="search-field md-input-field">
          <mat-label>Search tools...</mat-label>
          <input matInput formControlName="searchTerm" placeholder="Enter tool name or description"
                 class="md-input-base md-input-large">
          <mat-icon matPrefix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline" class="category-field md-input-field">
          <mat-label>Category</mat-label>
          <mat-select formControlName="category" class="md-input-base md-input-large">
            <mat-option *ngFor="let category of categories" [value]="category.value">
              <mat-icon>{{category.icon}}</mat-icon>
              {{category.label}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
  </div>

  <!-- Category Chips -->
  <div class="category-chips">
    <mat-chip-set>
      <mat-chip
        *ngFor="let category of categories"
        [class.selected]="selectedCategory === category.value"
        (click)="selectCategory(category.value)"
        class="category-chip md-chip-base md-chip-outlined">
        <mat-icon matChipAvatar>{{category.icon}}</mat-icon>
        {{category.label}}
      </mat-chip>
    </mat-chip-set>
  </div>

  <!-- Tools Grid -->
  <div class="tools-grid">
    <mat-card
      *ngFor="let tool of filteredTools; trackBy: trackByToolId"
      class="tool-card md-card-base md-card-elevated"
      [class.popular]="tool.popularity >= 80"
      matRipple>

      <!-- Tool Header -->
      <mat-card-header>
        <div class="tool-icon" mat-card-avatar>
          <mat-icon>{{getToolIcon(tool)}}</mat-icon>
        </div>
        <mat-card-title class="title-medium">{{tool.name}}</mat-card-title>
        <mat-card-subtitle class="body-medium">{{tool.category}}</mat-card-subtitle>

        <!-- Popularity Badge -->
        <div class="tool-badges">
          <mat-icon
            *ngIf="tool.popularity >= 80"
            class="popularity-badge"
            matTooltip="Popular tool"
            [style.color]="'var(--md-sys-color-primary)'">
            star
          </mat-icon>
          <span
            class="usage-badge label-small"
            [matBadge]="tool.usageCount"
            matBadgeColor="primary"
            matBadgeSize="small"
            matTooltip="Usage count">
          </span>
        </div>
      </mat-card-header>

      <!-- Tool Content -->
      <mat-card-content>
        <p class="body-medium tool-description">{{tool.description}}</p>

        <!-- Popularity Bar -->
        <div class="popularity-section">
          <div class="popularity-label">
            <span class="label-small">Popularity</span>
            <span class="label-small popularity-value">{{tool.popularity}}%</span>
          </div>
          <mat-progress-bar
            mode="determinate"
            [value]="tool.popularity"
            [color]="getPopularityColor(tool.popularity)">
          </mat-progress-bar>
        </div>

        <!-- Tool Parameters Preview -->
        <div class="parameters-preview" *ngIf="tool.parameters.length > 0">
          <span class="label-small parameters-label">Parameters:</span>
          <div class="parameters-chips">
            <mat-chip
              *ngFor="let param of tool.parameters.slice(0, 3)"
              class="parameter-chip label-small">
              {{param.name}}
              <span *ngIf="param.required" class="required-indicator">*</span>
            </mat-chip>
            <span *ngIf="tool.parameters.length > 3" class="label-small more-params">
              +{{tool.parameters.length - 3}} more
            </span>
          </div>
        </div>
      </mat-card-content>

      <!-- Tool Actions -->
      <mat-card-actions>
        <button
          mat-button
          color="primary"
          (click)="viewToolDetails(tool)"
          class="action-button md-button-base md-button-text md-button-medium">
          <mat-icon>info</mat-icon>
          Details
        </button>
        <button
          mat-raised-button
          color="primary"
          (click)="executeTool(tool)"
          [disabled]="!tool.isAvailable"
          class="action-button md-button-base md-button-filled md-button-medium">
          <mat-icon>play_arrow</mat-icon>
          Execute
        </button>
      </mat-card-actions>
    </mat-card>
  </div>

  <!-- Empty State -->
  <div *ngIf="filteredTools.length === 0" class="empty-state">
    <mat-icon class="empty-icon">search_off</mat-icon>
    <h3 class="headline-small">No tools found</h3>
    <p class="body-medium">Try adjusting your search criteria or category filter</p>
  </div>
</div>
