<div class="home-container">
  <!-- Hero Section -->
  <section class="hero-section" [class.animate-hero]="isHeroVisible">
    <div class="hero-content">
      <div class="hero-text">
        <h1 class="display-large">
          <span class="gradient-text">Assistant</span>
          <br>AI Development Companion
        </h1>
        <p class="body-large hero-subtitle">
          Revolutionize your development workflow with intelligent AI assistance. 
          From code analysis to database optimization, unlock the full potential of AI-powered development.
        </p>
        <div class="hero-actions">
          <button mat-fab extended color="primary" routerLink="/query"
                  class="cta-button animate-glow md-button-base md-button-filled md-button-extra-large">
            <mat-icon>psychology</mat-icon>
            Start Coding with AI
          </button>
          <button mat-stroked-button routerLink="/dashboard"
                  class="secondary-button md-button-base md-button-outlined md-button-large">
            <mat-icon>dashboard</mat-icon>
            View Dashboard
          </button>
        </div>
      </div>
      <div class="hero-visual">
        <div class="floating-cards">
          <div *ngFor="let feature of heroFeatures; let i = index" 
               #animatedCard
               class="feature-card"
               [class.animate-float]="isDataLoaded">
            <mat-icon [class]="'feature-icon-' + i">{{feature.icon}}</mat-icon>
            <h4 class="title-medium">{{feature.title}}</h4>
            <p class="body-medium">{{feature.description}}</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Stats Section -->
  <section class="stats-section" [class.animate-stats]="isStatsVisible">
    <div class="stats-grid">
      <div *ngFor="let stat of stats; let i = index" 
           #animatedStat
           class="stat-card">
        <mat-icon class="stat-icon">{{stat.icon}}</mat-icon>
        <div class="stat-content">
          <span class="headline-medium stat-value">{{stat.value}}</span>
          <span class="body-medium stat-label">{{stat.label}}</span>
          <span class="label-medium stat-trend">{{stat.trend}}</span>
        </div>
      </div>
    </div>
  </section>

  <!-- Quick Actions Section -->
  <section class="quick-actions-section">
    <div class="section-header">
      <h2 class="headline-medium">Quick Actions</h2>
      <p class="body-large">Jump into AI-powered development tools</p>
    </div>
    
    <div class="quick-actions-grid">
      <mat-card *ngFor="let action of quickActions; let i = index" 
                class="action-card state-layer motion-scale-in"
                [class]="action.color"
                [style.animation-delay]="(i * 150) + 'ms'"
                [routerLink]="action.route"
                matRipple>
        <mat-card-content>
          <div class="action-icon-container">
            <mat-icon class="action-icon">{{action.icon}}</mat-icon>
          </div>
          <h3 class="title-large action-title">{{action.title}}</h3>
          <p class="body-large action-description">{{action.description}}</p>
          <div class="action-arrow">
            <mat-icon>arrow_forward</mat-icon>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- System Status & Activity Section -->
  <section class="status-activity-section">
    <div class="section-grid">
      <!-- System Health Card -->
      <mat-card class="health-card elevated motion-slide-up">
        <mat-card-header>
          <mat-card-title class="title-large">
            <mat-icon>monitor_heart</mat-icon>
            System Health
          </mat-card-title>
          <div class="health-indicator" [class.healthy]="getSystemHealthLevel() > 80">
            <span class="body-medium health-percentage">{{getSystemHealthLevel() | number:'1.0-0'}}%</span>
          </div>
        </mat-card-header>
        <mat-card-content>
          <div class="health-progress">
            <mat-progress-bar 
              mode="determinate" 
              [value]="getSystemHealthLevel()"
              [color]="getSystemHealthLevel() > 80 ? 'primary' : 'warn'">
            </mat-progress-bar>
          </div>
          
          <div class="health-components" *ngIf="systemHealth">
            <div class="component-status">
              <mat-icon [class.healthy]="systemHealth.database.status === 'healthy'">storage</mat-icon>
              <span class="body-medium">Database</span>
              <span class="status-badge" [class]="systemHealth.database.status">
                {{systemHealth.database.status}}
              </span>
            </div>
            <div class="component-status">
              <mat-icon [class.healthy]="systemHealth.memory.status === 'healthy'">memory</mat-icon>
              <span class="body-medium">Memory</span>
              <span class="status-badge" [class]="systemHealth.memory.status">
                {{systemHealth.memory.status}}
              </span>
            </div>
            <div class="component-status">
              <mat-icon [class.healthy]="systemHealth.tools.status === 'healthy'">build</mat-icon>
              <span class="body-medium">Tools</span>
              <span class="status-badge" [class]="systemHealth.tools.status">
                {{systemHealth.tools.status}}
              </span>
            </div>
            <div class="component-status">
              <mat-icon [class.healthy]="systemHealth.api.status === 'healthy'">api</mat-icon>
              <span class="body-medium">API</span>
              <span class="status-badge" [class]="systemHealth.api.status">
                {{systemHealth.api.status}}
              </span>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Recent Activity Card -->
      <mat-card class="activity-card elevated motion-slide-up" [style.animation-delay]="'200ms'">
        <mat-card-header>
          <mat-card-title class="title-large">
            <mat-icon>timeline</mat-icon>
            Recent Activity
          </mat-card-title>
          <button mat-icon-button routerLink="/dashboard" class="md-icon-button">
            <mat-icon>open_in_new</mat-icon>
          </button>
        </mat-card-header>
        <mat-card-content>
          <div class="activity-list">
            <div *ngFor="let activity of recentActivities" class="activity-item">
              <div class="activity-icon">
                <mat-icon [class]="activity.type">
                  {{activity.type === 'query' ? 'chat' : activity.type === 'tool' ? 'build' : 'forum'}}
                </mat-icon>
              </div>
              <div class="activity-content">
                <p class="title-medium activity-title">{{activity.title}}</p>
                <p class="body-medium activity-description">{{activity.description}}</p>
                <span class="label-small activity-time">{{getTimeAgo(activity.timestamp)}}</span>
              </div>
              <div class="activity-status">
                <span class="status-dot" [class]="activity.status"></span>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Recent Conversations Card -->
      <mat-card class="conversations-card elevated motion-slide-up" [style.animation-delay]="'400ms'">
        <mat-card-header>
          <mat-card-title class="title-large">
            <mat-icon>chat_bubble_outline</mat-icon>
            Recent Conversations
          </mat-card-title>
          <button mat-icon-button routerLink="/conversations" class="md-icon-button">
            <mat-icon>open_in_new</mat-icon>
          </button>
        </mat-card-header>
        <mat-card-content>
          <div class="conversations-list">
            <div *ngFor="let conversation of recentConversations" 
                 class="conversation-item state-layer"
                 [routerLink]="['/conversations']"
                 matRipple>
              <div class="conversation-icon">
                <mat-icon>{{getStatusIcon(conversation.status)}}</mat-icon>
              </div>
              <div class="conversation-content">
                <h4 class="title-medium conversation-title">{{conversation.title}}</h4>
                <p class="body-medium conversation-message">{{conversation.lastMessage}}</p>
                <div class="conversation-meta">
                  <span class="label-small">{{conversation.messageCount}} messages</span>
                  <span class="label-small">{{getTimeAgo(conversation.lastMessageTime)}}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="empty-state" *ngIf="recentConversations.length === 0">
            <mat-icon>chat</mat-icon>
            <p class="body-large">No conversations yet</p>
            <button mat-button routerLink="/query" color="primary"
                    class="md-button-base md-button-text md-button-medium">
              Start your first conversation
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </section>

  <!-- Call to Action Section -->
  <section class="cta-section motion-fade-in">
    <div class="cta-content">
      <h2 class="headline-medium">Ready to Transform Your Development?</h2>
      <p class="body-large">
        Experience the power of AI-driven development assistance. 
        Start your first query and discover intelligent solutions to your coding challenges.
      </p>
      <div class="cta-actions">
        <button mat-raised-button color="primary" size="large" routerLink="/query"
                class="md-button-base md-button-filled md-button-extra-large">
          <mat-icon>rocket_launch</mat-icon>
          Get Started Now
        </button>
        <button mat-stroked-button routerLink="/tools"
                class="md-button-base md-button-outlined md-button-large">
          <mat-icon>explore</mat-icon>
          Explore Tools
        </button>
      </div>
    </div>
  </section>
</div>