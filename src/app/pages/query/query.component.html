<div class="query-interface">
  <div class="query-header">
    <h2>Intelligent Query Interface</h2>
    <p class="subtitle">Interact with your AI development assistant using natural language, code snippets, or file uploads</p>
  </div>

  <div class="query-layout">
    <!-- Context Awareness Panel -->
    <mat-card class="context-panel">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>visibility</mat-icon>
          Context Awareness
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <p class="context-description">Active contexts that the AI will consider:</p>
        <div class="context-chips">
          <mat-chip-row 
            *ngFor="let chip of contextChips" 
            [class.selected]="chip.active"
            (click)="toggleContextChip(chip)"
            class="context-chip">
            <mat-icon matChipAvatar>{{chip.icon}}</mat-icon>
            {{chip.label}}
          </mat-chip-row>
        </div>
        
        <mat-divider class="section-divider"></mat-divider>
        
        <p class="tools-description">Available tools to assist with your query:</p>
        <div class="tools-grid">
          <div 
            *ngFor="let tool of availableTools" 
            class="tool-chip"
            [class.active]="isToolActive(tool.id)"
            (click)="toggleTool(tool.id)"
            [matTooltip]="tool.description">
            <mat-icon>{{tool.icon}}</mat-icon>
            <span>{{tool.name}}</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Main Input Area -->
    <mat-card class="input-panel">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>chat</mat-icon>
          Multi-Modal Input
          <span class="input-type-indicator" *ngIf="selectedLanguage">
            <mat-icon>{{getLanguageIcon(selectedLanguage)}}</mat-icon>
            {{selectedLanguage | uppercase}}
          </span>
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <form [formGroup]="queryForm" (ngSubmit)="submitQuery()" class="query-form">
          <!-- Main Text Input -->
          <mat-form-field class="input-field md-input-field" appearance="outline">
            <mat-label>
              Describe what you need help with...
            </mat-label>
            <textarea
              matInput
              formControlName="content"
              placeholder="Example: Analyze this Go function for performance bottlenecks..."
              rows="6"
              class="query-textarea md-input-base md-input-large">
            </textarea>
            <mat-hint>
              <mat-icon class="hint-icon">lightbulb</mat-icon>
              Try typing code, asking questions, or describing a problem. The AI will auto-detect the content type.
            </mat-hint>
          </mat-form-field>

          <!-- File Upload Area -->
          <div class="file-upload-area" *ngIf="uploadedFiles.length > 0 || queryForm.get('type')?.value === 'file'">
            <div class="upload-header">
              <mat-icon>attach_file</mat-icon>
              <span>Attached Files</span>
            </div>
            <div class="uploaded-files">
              <div *ngFor="let file of uploadedFiles; let i = index" class="file-item">
                <mat-icon>description</mat-icon>
                <span class="file-name">{{file.name}}</span>
                <span class="file-size">({{(file.size / 1024) | number:'1.0-1'}} KB)</span>
                <button mat-icon-button (click)="removeFile(i)" class="remove-file">
                  <mat-icon>close</mat-icon>
                </button>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="action-row">
            <div class="file-actions">
              <input 
                #fileInput 
                type="file" 
                multiple 
                (change)="onFileSelect($event)"
                style="display: none;"
                accept=".ts,.js,.go,.py,.sql,.json,.md,.txt">
              
              <button
                mat-stroked-button
                type="button"
                (click)="fileInput.click()"
                class="file-button md-button-base md-button-outlined md-button-medium">
                <mat-icon>attach_file</mat-icon>
                Upload Files
              </button>

              <button
                mat-stroked-button
                type="button"
                (click)="clearQuery()"
                class="clear-button md-button-base md-button-outlined md-button-medium">
                <mat-icon>clear</mat-icon>
                Clear
              </button>
            </div>

            <div class="submit-actions">
              <button
                mat-raised-button
                color="primary"
                type="submit"
                [disabled]="!queryForm.get('content')?.value || isProcessing"
                class="submit-button md-button-base md-button-filled md-button-large">
                <mat-icon *ngIf="!isProcessing">send</mat-icon>
                <mat-icon *ngIf="isProcessing" class="spinning">sync</mat-icon>
                {{isProcessing ? 'Processing...' : 'Send Query'}}
              </button>
            </div>
          </div>
        </form>
      </mat-card-content>
    </mat-card>

    <!-- Progressive Response Area -->
    <mat-card class="response-panel" *ngIf="currentQuery || isProcessing">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>psychology</mat-icon>
          AI Response
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <!-- Processing State -->
        <div *ngIf="isProcessing" class="processing-state">
          <div class="thinking-animation">
            <mat-icon class="pulsing">psychology</mat-icon>
            <div class="thinking-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
          <p class="thinking-text">Analyzing your request and generating response...</p>
          <mat-progress-bar mode="indeterminate" class="progress-bar"></mat-progress-bar>
          <div class="thinking-steps">
            <div class="step active">
              <mat-icon>visibility</mat-icon>
              Understanding intent
            </div>
            <div class="step">
              <mat-icon>build</mat-icon>
              Selecting tools
            </div>
            <div class="step">
              <mat-icon>search</mat-icon>
              Searching knowledge
            </div>
            <div class="step">
              <mat-icon>create</mat-icon>
              Generating response
            </div>
          </div>
        </div>

        <!-- Response Content -->
        <div *ngIf="currentResponse && !isProcessing" class="response-content">
          <div class="response-header">
            <div class="response-meta">
              <span class="confidence-badge" [class.high]="currentResponse.confidence > 0.8">
                <mat-icon>trending_up</mat-icon>
                {{(currentResponse.confidence * 100) | number:'1.0-0'}}% Confidence
              </span>
              <span class="processing-time">
                <mat-icon>timer</mat-icon>
                {{currentResponse.processingTime}}ms
              </span>
            </div>
            <div class="tools-used" *ngIf="currentResponse.toolsUsed.length > 0">
              <span class="tools-label">Tools used:</span>
              <mat-chip-row *ngFor="let tool of currentResponse.toolsUsed" class="tool-used">
                <mat-icon matChipAvatar>{{getToolIcon(tool)}}</mat-icon>
                {{tool}}
              </mat-chip-row>
            </div>
          </div>

          <div class="response-body">
            <div class="response-text" [innerHTML]="currentResponse.content"></div>
          </div>

          <!-- Suggestions -->
          <div class="suggestions" *ngIf="currentResponse.suggestions.length > 0">
            <h4>Suggested next steps:</h4>
            <div class="suggestion-chips">
              <mat-chip-row *ngFor="let suggestion of currentResponse.suggestions" class="suggestion-chip">
                <mat-icon matChipAvatar>lightbulb</mat-icon>
                {{suggestion}}
              </mat-chip-row>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>