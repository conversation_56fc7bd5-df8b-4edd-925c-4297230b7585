import { <PERSON><PERSON><PERSON>, OnIni<PERSON>, After<PERSON>iew<PERSON>nit, <PERSON>Children, <PERSON>ry<PERSON>ist, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatRippleModule } from '@angular/material/core';
import { MockDataService } from '../../services/mock-data.service';
import { MemorySystem } from '../../models';

@Component({
  selector: 'app-memory',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatTabsModule,
    MatChipsModule,
    MatTooltipModule,
    MatRippleModule
  ],
  templateUrl: './memory.component.html',
  styleUrl: './memory.component.css'
})
export class MemoryComponent implements OnInit, AfterViewInit {
  @ViewChildren('memoryLayer') memoryLayers!: QueryList<ElementRef>;

  memorySystems: MemorySystem[] = [];
  selectedMemorySystem: MemorySystem | null = null;
  isVisualizationActive = false;

  memoryTypeInfo = {
    working: {
      icon: 'memory',
      color: 'primary',
      description: 'Temporary storage for current conversation context and immediate processing',
      characteristics: ['Fast access', 'Limited capacity', 'Volatile']
    },
    episodic: {
      icon: 'history',
      color: 'secondary',
      description: 'Memory of specific events, conversations, and user interactions',
      characteristics: ['Event-based', 'Time-stamped', 'Personal context']
    },
    semantic: {
      icon: 'school',
      color: 'tertiary',
      description: 'General knowledge, facts, and learned information',
      characteristics: ['Factual knowledge', 'Structured data', 'Domain expertise']
    },
    procedural: {
      icon: 'settings',
      color: 'success',
      description: 'Knowledge of how to perform tasks and execute procedures',
      characteristics: ['Skill-based', 'Action-oriented', 'Process knowledge']
    }
  };

  constructor(private mockDataService: MockDataService) {}

  ngOnInit() {
    this.loadMemorySystems();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.startVisualization();
    }, 1000);
  }

  loadMemorySystems() {
    this.mockDataService.getMemorySystems().subscribe(systems => {
      this.memorySystems = systems;
      if (systems.length > 0) {
        this.selectedMemorySystem = systems[0];
      }
    });
  }

  startVisualization() {
    this.isVisualizationActive = true;
    // Simulate memory flow animation
    this.animateMemoryFlow();
  }

  animateMemoryFlow() {
    // This would implement the memory flow visualization
    // For now, we'll just toggle the visualization state
    setInterval(() => {
      this.isVisualizationActive = !this.isVisualizationActive;
    }, 3000);
  }

  selectMemorySystem(system: MemorySystem) {
    this.selectedMemorySystem = system;
  }

  getUsagePercentage(system: MemorySystem): number {
    return Math.round((system.usage / system.capacity) * 100);
  }

  getUsageColor(percentage: number): string {
    if (percentage >= 90) return 'warn';
    if (percentage >= 70) return 'accent';
    return 'primary';
  }

  getMemoryTypeIcon(type: string): string {
    return this.memoryTypeInfo[type as keyof typeof this.memoryTypeInfo]?.icon || 'memory';
  }

  getMemoryTypeColor(type: string): string {
    return this.memoryTypeInfo[type as keyof typeof this.memoryTypeInfo]?.color || 'primary';
  }

  getMemoryTypeDescription(type: string): string {
    return this.memoryTypeInfo[type as keyof typeof this.memoryTypeInfo]?.description || '';
  }

  getMemoryCharacteristics(type: string): string[] {
    return this.memoryTypeInfo[type as keyof typeof this.memoryTypeInfo]?.characteristics || [];
  }

  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  refreshMemoryData() {
    this.loadMemorySystems();
  }

  optimizeMemory(system: MemorySystem) {
    console.log('Optimizing memory system:', system.name);
    // TODO: Implement memory optimization
  }
}
