.memory-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--md-sys-color-background);
  min-height: 100vh;
}

/* Header Section */
.memory-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  flex-wrap: wrap;
}

.title-section {
  flex: 1;
  min-width: 300px;
}

.title-section h1 {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 0 0 0.5rem 0;
  color: var(--md-sys-color-on-background);
}

.header-icon {
  font-size: 2.5rem !important;
  width: 2.5rem !important;
  height: 2.5rem !important;
  color: var(--md-sys-color-primary);
}

.header-subtitle {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
  max-width: 600px;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.action-button mat-icon {
  margin-right: 0.5rem;
}

/* Memory Visualization */
.memory-visualization {
  margin-bottom: 2rem;
}

.visualization-card {
  background: var(--md-sys-color-surface-container-low);
  border: 1px solid var(--md-sys-color-outline-variant);
  border-radius: var(--md-sys-shape-corner-large);
  overflow: hidden;
}

.memory-layers {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 2rem;
  position: relative;
  min-height: 300px;
  background: linear-gradient(135deg,
    var(--md-sys-color-surface-container-low) 0%,
    var(--md-sys-color-surface-container) 50%,
    var(--md-sys-color-surface-container-low) 100%);
}

.memory-layer {
  position: relative;
  background: var(--md-sys-color-surface-container);
  border: 2px solid var(--md-sys-color-outline-variant);
  border-radius: var(--md-sys-shape-corner-large);
  padding: 1.5rem;
  min-width: 200px;
  max-width: 250px;
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-medium2);
  z-index: 2;
}

.memory-layer:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: var(--md-sys-elevation-level4);
  border-color: var(--md-sys-color-primary);
}

.memory-layer.selected {
  border-color: var(--md-sys-color-primary);
  background: var(--md-sys-color-primary-container);
  transform: translateY(-4px);
  box-shadow: var(--md-sys-elevation-level3);
}

.layer-content {
  text-align: center;
}

.layer-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.layer-header mat-icon {
  font-size: 2rem !important;
  width: 2rem !important;
  height: 2rem !important;
}

.layer-header h3 {
  margin: 0;
  color: var(--md-sys-color-on-surface);
}

.layer-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.stat .label-small {
  color: var(--md-sys-color-on-surface-variant);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat .body-medium {
  color: var(--md-sys-color-on-surface);
  font-weight: 500;
}

.usage-bar {
  border-radius: var(--md-sys-shape-corner-small);
  height: 8px;
}

/* Connection Lines */
.connection-line {
  position: absolute;
  top: 50%;
  right: -30px;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg,
    var(--md-sys-color-primary) 0%,
    transparent 50%,
    var(--md-sys-color-primary) 100%);
  z-index: 1;
}

.memory-layers.active .connection-line {
  animation: connectionPulse 2s infinite;
}

@keyframes connectionPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* Memory Flow Animation */
.memory-flow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.flow-particle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: var(--md-sys-color-primary);
  border-radius: 50%;
  opacity: 0;
}

.memory-flow.active .flow-particle {
  animation: flowAnimation 4s infinite;
}

.memory-flow.active .flow-particle:nth-child(1) { animation-delay: 0s; }
.memory-flow.active .flow-particle:nth-child(2) { animation-delay: 0.8s; }
.memory-flow.active .flow-particle:nth-child(3) { animation-delay: 1.6s; }
.memory-flow.active .flow-particle:nth-child(4) { animation-delay: 2.4s; }
.memory-flow.active .flow-particle:nth-child(5) { animation-delay: 3.2s; }

@keyframes flowAnimation {
  0% {
    left: 10%;
    top: 50%;
    opacity: 0;
    transform: scale(0.5);
  }
  10% {
    opacity: 1;
    transform: scale(1);
  }
  90% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    left: 90%;
    top: 50%;
    opacity: 0;
    transform: scale(0.5);
  }
}

/* Memory Details */
.memory-details {
  margin-bottom: 2rem;
}

.details-card {
  background: var(--md-sys-color-surface-container-low);
  border: 1px solid var(--md-sys-color-outline-variant);
  border-radius: var(--md-sys-shape-corner-large);
}

.system-icon {
  width: 56px !important;
  height: 56px !important;
  border-radius: var(--md-sys-shape-corner-large);
  background: linear-gradient(135deg,
    var(--md-sys-color-primary-container),
    var(--md-sys-color-tertiary-container));
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--md-sys-color-outline-variant);
}

.system-icon mat-icon {
  font-size: 2rem !important;
  width: 2rem !important;
  height: 2rem !important;
}

.system-actions {
  margin-left: auto;
}

/* Memory Statistics */
.memory-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-medium);
  border: 1px solid var(--md-sys-color-outline-variant);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--md-sys-shape-corner-medium);
  background: var(--md-sys-color-surface-container);
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon mat-icon {
  font-size: 1.5rem !important;
  width: 1.5rem !important;
  height: 1.5rem !important;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-content .headline-small {
  margin: 0;
  color: var(--md-sys-color-on-surface);
}

.stat-content .body-medium {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}

/* Characteristics Section */
.characteristics-section {
  margin-bottom: 2rem;
}

.characteristics-section h4 {
  margin: 0 0 1rem 0;
  color: var(--md-sys-color-on-surface);
}

.characteristics-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.characteristic-chip {
  background: var(--md-sys-color-primary-container) !important;
  color: var(--md-sys-color-on-primary-container) !important;
  border: 1px solid var(--md-sys-color-primary) !important;
}

/* Usage Section */
.usage-section {
  margin-bottom: 1rem;
}

.usage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.usage-header h4 {
  margin: 0;
  color: var(--md-sys-color-on-surface);
}

.usage-percentage {
  color: var(--md-sys-color-primary);
  font-weight: 600;
}

.main-usage-bar {
  border-radius: var(--md-sys-shape-corner-small);
  height: 12px;
  margin-bottom: 0.5rem;
}

.usage-labels {
  display: flex;
  justify-content: space-between;
  color: var(--md-sys-color-on-surface-variant);
}

/* Memory Overview */
.memory-overview {
  margin-bottom: 2rem;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.overview-card {
  background: var(--md-sys-color-surface-container-low);
  border: 1px solid var(--md-sys-color-outline-variant);
  border-radius: var(--md-sys-shape-corner-large);
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-short2);
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--md-sys-elevation-level2);
  border-color: var(--md-sys-color-primary);
}

.overview-card.selected {
  border-color: var(--md-sys-color-primary);
  background: var(--md-sys-color-primary-container);
}

.overview-icon {
  width: 40px !important;
  height: 40px !important;
  border-radius: var(--md-sys-shape-corner-medium);
  background: linear-gradient(135deg,
    var(--md-sys-color-primary-container),
    var(--md-sys-color-secondary-container));
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--md-sys-color-outline-variant);
}

.overview-icon mat-icon {
  font-size: 1.25rem !important;
  width: 1.25rem !important;
  height: 1.25rem !important;
}

.overview-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.overview-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.overview-stat .label-small {
  color: var(--md-sys-color-on-surface-variant);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.overview-stat .body-medium {
  color: var(--md-sys-color-on-surface);
  font-weight: 500;
}

.overview-progress {
  border-radius: var(--md-sys-shape-corner-small);
  height: 6px;
}