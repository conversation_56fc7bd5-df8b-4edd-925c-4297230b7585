<div class="memory-container">
  <!-- Header Section -->
  <div class="memory-header">
    <div class="header-content">
      <div class="title-section">
        <h1 class="display-small">
          <mat-icon class="header-icon">account_tree</mat-icon>
          AI Memory Systems
        </h1>
        <p class="body-large header-subtitle">
          Visualize and understand how the AI manages different types of memory and knowledge
        </p>
      </div>

      <div class="header-actions">
        <button
          mat-raised-button
          color="primary"
          (click)="refreshMemoryData()"
          class="action-button">
          <mat-icon>refresh</mat-icon>
          Refresh Data
        </button>
      </div>
    </div>
  </div>

  <!-- Memory Layers Visualization -->
  <div class="memory-visualization">
    <mat-card class="visualization-card">
      <mat-card-header>
        <mat-card-title class="title-large">Memory Architecture</mat-card-title>
        <mat-card-subtitle>Interactive visualization of AI memory layers</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="memory-layers" [class.active]="isVisualizationActive">
          <div
            *ngFor="let system of memorySystems; let i = index"
            #memoryLayer
            class="memory-layer"
            [class]="'layer-' + system.type"
            [class.selected]="selectedMemorySystem?.id === system.id"
            (click)="selectMemorySystem(system)"
            matRipple>

            <div class="layer-content">
              <div class="layer-header">
                <mat-icon [color]="getMemoryTypeColor(system.type)">
                  {{getMemoryTypeIcon(system.type)}}
                </mat-icon>
                <h3 class="title-medium">{{system.name}}</h3>
              </div>

              <div class="layer-stats">
                <div class="stat">
                  <span class="label-small">Usage</span>
                  <span class="body-medium">{{getUsagePercentage(system)}}%</span>
                </div>
                <div class="stat">
                  <span class="label-small">Capacity</span>
                  <span class="body-medium">{{formatBytes(system.capacity)}}</span>
                </div>
              </div>

              <mat-progress-bar
                mode="determinate"
                [value]="getUsagePercentage(system)"
                [color]="getUsageColor(getUsagePercentage(system))"
                class="usage-bar">
              </mat-progress-bar>
            </div>

            <!-- Connection Lines -->
            <div class="connection-line" *ngIf="i < memorySystems.length - 1"></div>
          </div>
        </div>

        <!-- Flow Animation -->
        <div class="memory-flow" [class.active]="isVisualizationActive">
          <div class="flow-particle" *ngFor="let particle of [1,2,3,4,5]"></div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Memory System Details -->
  <div class="memory-details" *ngIf="selectedMemorySystem">
    <mat-card class="details-card">
      <mat-card-header>
        <div class="system-icon" mat-card-avatar>
          <mat-icon [color]="getMemoryTypeColor(selectedMemorySystem.type)">
            {{getMemoryTypeIcon(selectedMemorySystem.type)}}
          </mat-icon>
        </div>
        <mat-card-title class="title-large">{{selectedMemorySystem.name}}</mat-card-title>
        <mat-card-subtitle class="body-medium">
          {{getMemoryTypeDescription(selectedMemorySystem.type)}}
        </mat-card-subtitle>

        <div class="system-actions">
          <button
            mat-icon-button
            color="primary"
            (click)="optimizeMemory(selectedMemorySystem)"
            matTooltip="Optimize memory usage">
            <mat-icon>tune</mat-icon>
          </button>
        </div>
      </mat-card-header>

      <mat-card-content>
        <!-- Memory Statistics -->
        <div class="memory-stats">
          <div class="stat-card">
            <div class="stat-icon">
              <mat-icon color="primary">storage</mat-icon>
            </div>
            <div class="stat-content">
              <span class="headline-small">{{formatBytes(selectedMemorySystem.usage)}}</span>
              <span class="body-medium">Used</span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <mat-icon color="accent">memory</mat-icon>
            </div>
            <div class="stat-content">
              <span class="headline-small">{{formatBytes(selectedMemorySystem.capacity)}}</span>
              <span class="body-medium">Total</span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <mat-icon [color]="selectedMemorySystem.isActive ? 'primary' : 'warn'">
                {{selectedMemorySystem.isActive ? 'check_circle' : 'pause_circle'}}
              </mat-icon>
            </div>
            <div class="stat-content">
              <span class="headline-small">{{selectedMemorySystem.isActive ? 'Active' : 'Inactive'}}</span>
              <span class="body-medium">Status</span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <mat-icon color="accent">schedule</mat-icon>
            </div>
            <div class="stat-content">
              <span class="headline-small">{{selectedMemorySystem.lastAccess | date:'short'}}</span>
              <span class="body-medium">Last Access</span>
            </div>
          </div>
        </div>

        <!-- Memory Characteristics -->
        <div class="characteristics-section">
          <h4 class="title-medium">Characteristics</h4>
          <div class="characteristics-chips">
            <mat-chip
              *ngFor="let characteristic of getMemoryCharacteristics(selectedMemorySystem.type)"
              class="characteristic-chip">
              {{characteristic}}
            </mat-chip>
          </div>
        </div>

        <!-- Usage Progress -->
        <div class="usage-section">
          <div class="usage-header">
            <h4 class="title-medium">Memory Usage</h4>
            <span class="body-large usage-percentage">
              {{getUsagePercentage(selectedMemorySystem)}}%
            </span>
          </div>
          <mat-progress-bar
            mode="determinate"
            [value]="getUsagePercentage(selectedMemorySystem)"
            [color]="getUsageColor(getUsagePercentage(selectedMemorySystem))"
            class="main-usage-bar">
          </mat-progress-bar>
          <div class="usage-labels">
            <span class="label-small">{{formatBytes(selectedMemorySystem.usage)}} used</span>
            <span class="label-small">{{formatBytes(selectedMemorySystem.capacity - selectedMemorySystem.usage)}} available</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Memory Overview Grid -->
  <div class="memory-overview">
    <div class="overview-grid">
      <mat-card
        *ngFor="let system of memorySystems"
        class="overview-card"
        [class.selected]="selectedMemorySystem?.id === system.id"
        (click)="selectMemorySystem(system)"
        matRipple>

        <mat-card-header>
          <div class="overview-icon" mat-card-avatar>
            <mat-icon [color]="getMemoryTypeColor(system.type)">
              {{getMemoryTypeIcon(system.type)}}
            </mat-icon>
          </div>
          <mat-card-title class="title-medium">{{system.name}}</mat-card-title>
          <mat-card-subtitle class="body-small">{{system.type}}</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <div class="overview-stats">
            <div class="overview-stat">
              <span class="label-small">Usage</span>
              <span class="body-medium">{{getUsagePercentage(system)}}%</span>
            </div>
            <div class="overview-stat">
              <span class="label-small">Size</span>
              <span class="body-medium">{{formatBytes(system.capacity)}}</span>
            </div>
          </div>

          <mat-progress-bar
            mode="determinate"
            [value]="getUsagePercentage(system)"
            [color]="getUsageColor(getUsagePercentage(system))"
            class="overview-progress">
          </mat-progress-bar>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
